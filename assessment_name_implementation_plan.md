# Assessment Name Implementation Plan

## 🗄️ Database Schema Changes

### PostgreSQL Queries

```sql
-- 1. Add assessment_name column to analysis_jobs table
ALTER TABLE archive.analysis_jobs 
ADD COLUMN assessment_name VARCHAR(255) NOT NULL DEFAULT 'AI-Driven Talent Mapping';

-- 2. Add assessment_name column to analysis_results table  
ALTER TABLE archive.analysis_results 
ADD COLUMN assessment_name VARCHAR(255) NOT NULL DEFAULT 'AI-Driven Talent Mapping';

-- 3. Create index for better query performance
CREATE INDEX idx_analysis_jobs_assessment_name ON archive.analysis_jobs(assessment_name);
CREATE INDEX idx_analysis_results_assessment_name ON archive.analysis_results(assessment_name);

-- 4. Add check constraint for valid assessment names (optional)
ALTER TABLE archive.analysis_jobs 
ADD CONSTRAINT chk_analysis_jobs_assessment_name 
CHECK (assessment_name IN ('AI-Driven Talent Mapping', 'AI-Based IQ Test', 'Custom Assessment'));

ALTER TABLE archive.analysis_results 
ADD CONSTRAINT chk_analysis_results_assessment_name 
CHECK (assessment_name IN ('AI-Driven Talent Mapping', 'AI-Based IQ Test', 'Custom Assessment'));

-- 5. Verify changes
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_schema = 'archive' 
AND table_name IN ('analysis_jobs', 'analysis_results')
AND column_name = 'assessment_name';
```

## 📋 Implementation Plan by Service

### 🏗️ **Archive Service** (Priority: HIGH)

#### Files yang Berubah:

**1. `src/models/AnalysisJob.js`**
- **Kenapa berubah**: Model Sequelize harus sync dengan schema database baru
- **Hubungan database**: Direct mapping ke kolom `assessment_name` di tabel `analysis_jobs`
- **Yang diperhatikan**: 
  - Tambah field definition dengan validation
  - Update indexes array jika perlu
  - Pastikan default value konsisten dengan database

```javascript
assessment_name: {
  type: DataTypes.STRING(255),
  allowNull: false,
  defaultValue: 'AI-Driven Talent Mapping',
  field: 'assessment_name',
  validate: {
    isIn: [['AI-Driven Talent Mapping', 'AI-Based IQ Test', 'Custom Assessment']]
  }
}
```

**2. `src/models/AnalysisResult.js`**
- **Kenapa berubah**: Model harus reflect kolom baru di `analysis_results`
- **Hubungan database**: Direct mapping ke kolom `assessment_name`
- **Yang diperhatikan**: Sama seperti AnalysisJob model

**3. `src/utils/validation.js`**
- **Kenapa berubah**: API validation harus handle field baru
- **Hubungan database**: Memastikan data valid sebelum masuk database
- **Yang diperhatikan**:
  - Update `createAnalysisJobSchema`
  - Update `createAnalysisResultSchema`
  - Buat assessment_name optional dengan default value

**4. `src/controllers/resultsController.js`**
- **Kenapa berubah**: Controller harus handle dan return assessment_name
- **Hubungan database**: Mengambil dan menyimpan data assessment_name
- **Yang diperhatikan**:
  - Update response formatting
  - Pastikan assessment_name included dalam queries
  - Handle backward compatibility

**5. `src/routes/results.js`**
- **Kenapa berubah**: Route validation harus updated
- **Hubungan database**: Indirect - memastikan data valid sebelum ke database
- **Yang diperhatikan**: Pastikan validation middleware terupdate

### 🎯 **Assessment Service** (Priority: HIGH)

#### Files yang Berubah:

**1. `src/routes/assessments.js`**
- **Kenapa berubah**: Endpoint `/submit` harus accept assessment_name parameter
- **Hubungan database**: Data ini akan disimpan ke analysis_jobs table
- **Yang diperhatikan**:
  - Extract assessment_name dari request body
  - Set default jika tidak provided
  - Pass ke queue service dan archive service

**2. `src/services/queueService.js`**
- **Kenapa berubah**: Message ke RabbitMQ harus include assessment_name
- **Hubungan database**: Data ini akan digunakan worker untuk save ke database
- **Yang diperhatikan**:
  - Update message structure
  - Backward compatibility dengan old message format
  - Include assessment_name dalam message headers

**3. `src/services/archiveService.js`**
- **Kenapa berubah**: Calls ke archive service harus include assessment_name
- **Hubungan database**: Data dikirim ke archive service untuk disimpan
- **Yang diperhatikan**:
  - Update `createJob()` method
  - Handle response yang include assessment_name

**4. `src/schemas/assessment.js`** (Optional)
- **Kenapa berubah**: Jika ingin validate assessment_name di level schema
- **Hubungan database**: Validation sebelum data masuk sistem
- **Yang diperhatikan**: Buat assessment_name optional field

### ⚙️ **Analysis Worker** (Priority: MEDIUM)

#### Files yang Berubah:

**1. `src/utils/validator.js`**
- **Kenapa berubah**: Job message validation harus handle assessment_name
- **Hubungan database**: Memastikan data valid sebelum processing
- **Yang diperhatikan**:
  - Update `jobMessageSchema`
  - Handle optional assessment_name
  - Backward compatibility

**2. `src/processors/assessmentProcessor.js`**
- **Kenapa berubah**: Processor harus pass assessment_name ke archive service
- **Hubungan database**: Data akan disimpan ke analysis_results table
- **Yang diperhatikan**:
  - Extract assessment_name dari job data
  - Pass ke `saveAnalysisResult()`

**3. `src/services/archiveService.js`**
- **Kenapa berubah**: Save result harus include assessment_name
- **Hubungan database**: Direct save ke analysis_results table
- **Yang diperhatikan**:
  - Update `saveAnalysisResult()` method
  - Include assessment_name dalam request body

### 🌐 **API Gateway** (Priority: LOW)

#### Files yang Mungkin Berubah:

**1. `src/routes/index.js`**
- **Kenapa berubah**: Jika ada logic routing berdasarkan assessment type
- **Hubungan database**: Indirect - routing logic
- **Yang diperhatikan**: Rate limiting per assessment type

## 🔄 Data Flow dengan Assessment Name

```
1. Client Request → Assessment Service
   POST /assessments/submit
   Body: { assessmentData, assessmentName? }

2. Assessment Service → RabbitMQ Queue
   Message: { jobId, userId, userEmail, assessmentData, assessmentName }

3. Assessment Service → Archive Service  
   POST /jobs
   Body: { job_id, user_id, assessment_data, assessment_name }

4. Analysis Worker ← RabbitMQ Queue
   Process: Extract assessmentName from message

5. Analysis Worker → Archive Service
   POST /results  
   Body: { user_id, assessment_data, persona_profile, assessment_name }

6. Database Storage
   INSERT INTO analysis_jobs (..., assessment_name)
   INSERT INTO analysis_results (..., assessment_name)
```

## ⚠️ Critical Development Considerations

### **1. Backward Compatibility**
- **Problem**: Existing data dan old message format
- **Solution**: 
  - Default values dalam database
  - Graceful handling di worker untuk old messages
  - API tetap accept request tanpa assessment_name

### **2. Data Consistency**
- **Problem**: assessment_name harus konsisten antara jobs dan results
- **Solution**:
  - Pass assessment_name dari job ke result
  - Validation di semua layer
  - Foreign key relationship consideration

### **3. Queue Message Versioning**
- **Problem**: Worker harus handle old dan new message format
- **Solution**:
```javascript
// Worker validation
const assessmentName = jobData.assessmentName || 'AI-Driven Talent Mapping';
```

### **4. Database Performance**
- **Problem**: Query performance dengan field baru
- **Solution**: 
  - Index pada assessment_name
  - Optimize queries yang filter by assessment_name

### **5. Testing Strategy**
- **Unit tests**: Model validation, API endpoints
- **Integration tests**: End-to-end flow dengan assessment_name
- **Migration tests**: Database schema changes
- **Backward compatibility tests**: Old format handling

## 🚀 Implementation Phases

### **Phase 1: Database & Models (Day 1)**
1. Run PostgreSQL queries
2. Update Sequelize models
3. Test database connectivity

### **Phase 2: Archive Service (Day 2)**
4. Update validation schemas
5. Update controllers dan routes
6. Test archive service endpoints

### **Phase 3: Assessment Service (Day 3)**
7. Update assessment submission endpoint
8. Update queue service
9. Test assessment flow

### **Phase 4: Analysis Worker (Day 4)**
10. Update worker validation
11. Update processing logic
12. Test worker processing

### **Phase 5: Integration & Testing (Day 5)**
13. End-to-end testing
14. Performance testing
15. Documentation update

## 📊 Monitoring & Rollback Plan

### **Monitoring Points**
- Database query performance
- Queue message processing success rate
- API response times
- Error rates by assessment type

### **Rollback Strategy**
1. **Database**: Keep old columns, add new ones
2. **Code**: Feature flags untuk enable/disable assessment_name
3. **Queue**: Worker handle both message formats
4. **API**: assessment_name optional parameter

### **Success Metrics**
- Zero downtime deployment
- No data loss
- All existing functionality works
- New assessment_name feature functional