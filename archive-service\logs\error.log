{"error":"listen EADDRINUSE: address already in use :::3002","level":"error","message":"Uncaught exception","stack":"Error: listen EADDRINUSE: address already in use :::3002\n    at Server.setupListenHandle [as _listen2] (node:net:1940:16)\n    at listenInCluster (node:net:1997:12)\n    at Server.listen (node:net:2102:7)\n    at Function.listen (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\express\\lib\\application.js:635:24)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\app.js:144:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-20 10:06:10:610"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/83fa5ec5-a72d-4c9e-b39e-39f858577c1c/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:40:4140"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/839eadab-2d16-475e-b33b-de13158d7100/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:40:4140"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/02ed6e8a-c439-41dc-aebc-38e9e9f1f2c5/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:40:4140"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/fb78bdb8-234e-4700-b20b-60c402108309/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:40:4140"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/4ea9fd5a-a43d-4661-964f-c4e105f820bb/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:40:4140"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/2a089ae6-10f3-4c7e-bcaa-fda238fa30f7/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:40:4140"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/5110ad19-a5f9-4094-8eb2-b348dbdbf6b2/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:40:4140"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/e07cd077-bfca-4a60-ac00-b971db90cfa7/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:40:4140"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/7159b322-1d22-4bde-a29d-1ee19c4c7d67/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:40:4140"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/fd55fe67-45d2-41d6-aa19-eb7a865d67b7/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:40:4140"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/ede5ec42-af54-4f93-b936-96f0294ed908/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:41:4141"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/cad0d671-360b-4ebb-b3c9-904e99cfd358/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:41:4141"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/7e144cb6-3bd4-46c9-ac9c-0ecf9a27b63d/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:41:4141"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/54d11b54-2dc9-4449-92fc-23ec5714f48d/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:41:4141"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/a6b0e559-4131-4c2f-a76a-f7d2ffde5568/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:41:4141"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/aa974f0f-1939-4ac3-85e3-14370e3a50e8/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:41:4141"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/5370cd41-c162-47f9-a631-6694cbc7270f/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:41:4141"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/08980268-2c3a-4918-8466-cb68f47a3a14/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:41:4141"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/48179fce-84fe-45d9-8187-25035e4f6c02/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:41:4141"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/eae007a5-083d-4d42-b5e5-e07c6793b871/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:41:4141"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/b8384c14-6dcb-48f6-8244-197b8c91b124/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:42:4142"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/94d3de60-906e-4eb7-b7fc-db107419ee9f/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:42:4142"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/72345d27-301c-4adb-bab4-0e2c8bace3ec/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:42:4142"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/9fbccb47-2db1-4d77-8ae7-18fd229bb8ca/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:42:4142"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/e8bfd2cf-2507-43d8-b13d-29d7b289884c/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:42:4142"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/ec88b27c-6c71-4758-9857-076020bef935/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:42:4142"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/b2d1bdb1-51ab-4ee8-8dea-b25b7430737b/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:42:4142"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/04934344-c974-491b-b42f-39a50fdfd936/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:42:4142"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/7120a28e-d49c-46da-b2ac-f232a404c81e/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:42:4142"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/6ad7c259-12bd-46ce-9a64-7bc6bc8e8bce/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:42:4142"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/d9d9155a-3e10-4be4-9b76-fa3491570515/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:43:4143"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/bddd72d4-da03-4a6a-844d-90980dde5bd1/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:43:4143"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/c6171c94-e01d-4b7e-b157-207909a9e802/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:43:4143"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/6bbdf453-32d5-407e-8633-77bea75cc502/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:43:4143"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/de40c30c-3db4-44f9-b5df-5b32776f0b54/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:43:4143"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/e8450020-0dd6-4c04-b1e0-87ca98fabd94/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:43:4143"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/88de42f4-cd8f-4983-9939-72f9981c615f/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:43:4143"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/44c8a7f7-84e1-46ea-bdec-79cb681badca/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:43:4143"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/b9c8e7bf-b759-4532-9895-d661f7829e2e/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:43:4143"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/bbbbb6ac-4b32-4636-ab07-565207466843/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:43:4143"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/b6e31852-f889-4eff-b50b-2944c4da572f/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:44:4144"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/95de3dfd-bda7-4159-a0eb-d90e036f5b45/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:44:4144"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/9bc9fc6e-8927-4018-83be-a75d9ba59b61/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:44:4144"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/2ee18f2a-e040-4ef8-bfff-eb0e4fee9e00/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:44:4144"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/e451fd7f-880b-4145-915c-21fdee9878a0/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:44:4144"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/8e463987-a216-4a80-9c47-eabb1b4e0f6d/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:44:4144"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/3186d36b-2b3e-4861-aa69-1aa06ef90b12/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:44:4144"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/ce18e0db-36f8-434c-8be1-97398bba0c4b/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:44:4144"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/f40112ac-cfc1-4734-a5bc-706294f68138/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:44:4144"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/391008b3-59f4-42a4-92e0-beed566628c4/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:44:4144"}
{"count":6,"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Bulk insert failed","timestamp":"2025-07-20 10:41:58:4158"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":0,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:41:58:4158","userId":"a41976cc-4a27-44a7-9595-d409e48539be"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":1,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:41:58:4158","userId":"f021f043-4935-4be2-b42f-ccdb69d4a5e6"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":2,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:41:58:4158","userId":"60f711bc-4e2b-448a-9a77-14b54f829e3d"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":3,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:41:58:4158","userId":"323f45ce-4ec5-4858-9a9a-545830e452c8"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":4,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:41:58:4158","userId":"b5425449-e8f5-4f48-902a-1ed3df899be9"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":5,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:41:58:4158","userId":"cc739760-9702-414e-95f2-da047d369143"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/2a089ae6-10f3-4c7e-bcaa-fda238fa30f7/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:58:4158"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/b2d1bdb1-51ab-4ee8-8dea-b25b7430737b/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:58:4158"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/54d11b54-2dc9-4449-92fc-23ec5714f48d/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:58:4158"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/7159b322-1d22-4bde-a29d-1ee19c4c7d67/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:58:4158"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/e07cd077-bfca-4a60-ac00-b971db90cfa7/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:58:4158"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/02ed6e8a-c439-41dc-aebc-38e9e9f1f2c5/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:41:58:4158"}
{"count":11,"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Bulk insert failed","timestamp":"2025-07-20 10:42:00:420"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":0,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:00:420","userId":"dc6ee2dd-30ae-4188-916d-f5d4ca315f51"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":1,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:00:420","userId":"b3c545ee-e0c1-4a2c-9219-2ec5ecb2aa12"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":2,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:00:420","userId":"5fb47e5f-46c6-4d37-8b33-5a235281c777"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":3,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:00:420","userId":"7813975f-f934-41ba-a51a-aae0253074d7"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":4,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:00:420","userId":"8da69425-bc20-408b-8ba1-a79b6c2a1e30"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":5,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:00:420","userId":"cdf9b892-bf6f-44c0-a555-5a67f2b8691a"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":6,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:00:420","userId":"193e8c90-4efd-476c-b441-0927c6e2f246"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":7,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:00:420","userId":"f1c35d09-d758-4c5c-8a95-aa6d30a1c14f"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":8,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:00:420","userId":"54568bfb-bab9-480b-be90-e0c4d1ad1e86"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":9,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:00:420","userId":"aba0c47a-33b0-443b-a264-5e9053fea0ea"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":10,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:00:420","userId":"93fbd45c-1dc6-4d45-9025-93a2a9378fb4"}
{"count":1,"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Bulk insert failed","timestamp":"2025-07-20 10:42:00:420"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/fb78bdb8-234e-4700-b20b-60c402108309/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:00:420"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/b8384c14-6dcb-48f6-8244-197b8c91b124/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:00:420"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/ede5ec42-af54-4f93-b936-96f0294ed908/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:00:420"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/2ee18f2a-e040-4ef8-bfff-eb0e4fee9e00/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:00:420"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/83fa5ec5-a72d-4c9e-b39e-39f858577c1c/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:00:420"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/6ad7c259-12bd-46ce-9a64-7bc6bc8e8bce/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:00:420"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/5370cd41-c162-47f9-a631-6694cbc7270f/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:00:420"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/9bc9fc6e-8927-4018-83be-a75d9ba59b61/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:00:420"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/9fbccb47-2db1-4d77-8ae7-18fd229bb8ca/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:00:420"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":0,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:00:420","userId":"f3390657-a2cd-4530-a374-2a3c1f45286a"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/de40c30c-3db4-44f9-b5df-5b32776f0b54/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:00:420"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/08980268-2c3a-4918-8466-cb68f47a3a14/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:00:420"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/839eadab-2d16-475e-b33b-de13158d7100/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:00:420"}
{"count":3,"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Bulk insert failed","timestamp":"2025-07-20 10:42:00:420"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":0,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:00:420","userId":"2518e0dc-b877-4165-b464-d6a1d2f9faf5"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":1,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:00:420","userId":"6dcad023-c1db-4c8f-a1bd-096be3ff44bf"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":2,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:00:420","userId":"d75127f2-5917-4699-9ce0-231557249954"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/72345d27-301c-4adb-bab4-0e2c8bace3ec/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:00:420"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/7e144cb6-3bd4-46c9-ac9c-0ecf9a27b63d/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:00:420"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/44c8a7f7-84e1-46ea-bdec-79cb681badca/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:00:420"}
{"count":20,"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Bulk insert failed","timestamp":"2025-07-20 10:42:02:422"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":0,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:02:422","userId":"97c9f640-13bf-46fe-9961-686d61bf92f8"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":1,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:02:422","userId":"156fb4cc-9998-4644-a575-c3838537a679"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":2,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:02:422","userId":"7cc4aa3a-54e6-4693-aefd-6e9b26afdf4f"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":3,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:02:422","userId":"43c1c6f3-cfdc-4887-adfd-78677a09b7b5"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":4,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:02:422","userId":"73695504-2e17-47c1-b607-e7663f10e766"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":5,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:02:422","userId":"0cfc6127-d5bb-49a8-a535-026b86870f49"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":6,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:02:422","userId":"46c862d7-09f2-40bc-8c3b-52e0da194652"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":7,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:02:422","userId":"ee83d62e-6847-400d-816e-cc1832f26151"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":8,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:02:422","userId":"3ac7a54a-9fe1-442b-b19c-446fd3147d78"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":9,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:02:422","userId":"f4adb72f-0300-4429-8025-d28a22a7ef07"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":10,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:02:422","userId":"6db963a7-7828-44db-a3e9-69807a6becb9"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":11,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:02:422","userId":"896ab44f-9efa-40e9-9487-87ce4bb7a928"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":12,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:02:422","userId":"ef9068c3-66dd-4f64-9255-060533918eb8"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":13,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:02:422","userId":"de3537d5-6521-4892-8124-28b7fe6dae5d"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":14,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:02:422","userId":"77cbdea1-450d-45ba-ac9b-3d93113fd29f"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":15,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:02:422","userId":"af9ebb36-ba16-4767-937c-c174d403f9d1"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":16,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:02:422","userId":"568dc38d-312c-4db0-adf3-18bc743a8317"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":17,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:02:422","userId":"27bcb8fd-4deb-48df-baa3-bff31845502e"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":18,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:02:422","userId":"f3fa7032-ae96-4b2d-b425-56589fb77663"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":19,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:02:422","userId":"87053593-4667-421e-a25c-8c1cbb9a59e5"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/5110ad19-a5f9-4094-8eb2-b348dbdbf6b2/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:02:422"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/cad0d671-360b-4ebb-b3c9-904e99cfd358/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:02:422"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/eae007a5-083d-4d42-b5e5-e07c6793b871/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:02:422"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/ec88b27c-6c71-4758-9857-076020bef935/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:02:422"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/3186d36b-2b3e-4861-aa69-1aa06ef90b12/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:02:422"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/c6171c94-e01d-4b7e-b157-207909a9e802/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:02:422"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/e8bfd2cf-2507-43d8-b13d-29d7b289884c/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:02:422"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/04934344-c974-491b-b42f-39a50fdfd936/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:02:422"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/88de42f4-cd8f-4983-9939-72f9981c615f/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:02:422"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/bddd72d4-da03-4a6a-844d-90980dde5bd1/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:02:422"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/94d3de60-906e-4eb7-b7fc-db107419ee9f/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:02:422"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/8e463987-a216-4a80-9c47-eabb1b4e0f6d/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:02:422"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/4ea9fd5a-a43d-4661-964f-c4e105f820bb/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:02:422"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/b9c8e7bf-b759-4532-9895-d661f7829e2e/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:02:422"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/7120a28e-d49c-46da-b2ac-f232a404c81e/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:02:422"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/ce18e0db-36f8-434c-8be1-97398bba0c4b/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:02:422"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/d9d9155a-3e10-4be4-9b76-fa3491570515/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:02:422"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/e8450020-0dd6-4c04-b1e0-87ca98fabd94/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:02:422"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/b6e31852-f889-4eff-b50b-2944c4da572f/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:02:422"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/e451fd7f-880b-4145-915c-21fdee9878a0/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:02:422"}
{"count":7,"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Bulk insert failed","timestamp":"2025-07-20 10:42:04:424"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":0,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:04:424","userId":"4cd7f413-0892-4ab7-8e67-e23372284049"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":1,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:04:424","userId":"69c1019a-10d2-4fcc-9283-a0623568b127"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":2,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:04:424","userId":"ff430091-4f15-40d7-8daa-070887dc2ad9"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":3,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:04:424","userId":"d4981c79-f343-4f7a-82cf-fdeed782cb22"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":4,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:04:424","userId":"a96c4c5d-3a77-4c0f-b4fb-a29118d1e452"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":5,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:04:424","userId":"ec6600d9-60d2-4a98-b71a-9e67941075f6"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":6,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:04:424","userId":"cb2ce36e-31fc-4b3f-9bed-49e9581f04bc"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/95de3dfd-bda7-4159-a0eb-d90e036f5b45/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:04:424"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/aa974f0f-1939-4ac3-85e3-14370e3a50e8/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:04:424"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/391008b3-59f4-42a4-92e0-beed566628c4/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:04:424"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/6bbdf453-32d5-407e-8633-77bea75cc502/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:04:424"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/fd55fe67-45d2-41d6-aa19-eb7a865d67b7/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:04:424"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/48179fce-84fe-45d9-8187-25035e4f6c02/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:04:424"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/a6b0e559-4131-4c2f-a76a-f7d2ffde5568/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:04:424"}
{"count":2,"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","level":"error","message":"Bulk insert failed","timestamp":"2025-07-20 10:42:07:427"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":0,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:07:427","userId":"a47cdda2-c722-40a5-ace5-cad082f52367"}
{"error":"insert or update on table \"analysis_results\" violates foreign key constraint \"fk_analysis_results_user_id\"","index":1,"level":"error","message":"Individual insert failed","timestamp":"2025-07-20 10:42:07:427","userId":"126b3f74-3b95-403d-9443-515f2d94109b"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/bbbbb6ac-4b32-4636-ab07-565207466843/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:07:427"}
{"error":"Transaction cannot be rolled back because it has been finished with state: rollback","ip":"::1","level":"error","message":"Error occurred","method":"PUT","path":"/archive/jobs/f40112ac-cfc1-4734-a5bc-706294f68138/status","stack":"Error: Transaction cannot be rolled back because it has been finished with state: rollback\n    at Transaction.rollback (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\sequelize\\lib\\transaction.js:59:13)\n    at AnalysisJobsService.updateJobStatus (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\services\\analysisJobsService.js:132:25)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\routes\\results.js:195:19","timestamp":"2025-07-20 10:42:07:427"}
